# JeePlus 项目目录结构总结

## 项目概述
这是一个基于 Spring Boot 2.3.5 的企业级法律案件管理系统，采用前后端分离架构，支持多端访问（Web管理端、用户端、移动端）。

## 📂 **完整目录结构**

```
jeeplus-parent/
├── 📁 jeeplus/                    # 主应用模块
│   ├── 📁 jeeplus-web/           # Web启动模块 (主要运行入口)
│   ├── 📁 jeeplus-platform/      # 平台核心模块
│   │   ├── 📁 jeeplus-admin/     # 管理功能模块
│   │   └── 📁 jeeplus-core/      # 核心功能模块
│   ├── 📁 jeeplus-plugins/       # 插件模块
│   │   ├── 📁 jeeplus-monitor/   # 系统监控
│   │   ├── 📁 jeeplus-tools/     # 工具插件
│   │   ├── 📁 jeeplus-quartz/    # 定时任务
│   │   ├── 📁 jeeplus-datasource/# 数据源
│   │   ├── 📁 jeeplus-oa/        # 办公自动化
│   │   ├── 📁 jeeplus-ureport/   # 报表
│   │   └── 📁 jeeplus-wps/       # WPS文档
│   └── 📁 jeeplus-module/        # 业务模块
│       ├── 📁 jeeplus-case/      # 案件管理
│       └── 📁 jeeplus-api/       # API接口
├── 📁 jeeplus-ord/               # 订单管理模块 (若依框架)
│   ├── 📁 lawcase-admin/         # 订单管理后台
│   ├── 📁 lawcase-common/        # 通用组件
│   ├── 📁 lawcase-framework/     # 框架核心
│   ├── 📁 lawcase-generator/     # 代码生成器
│   ├── 📁 lawcase-mall/          # 商城订单
│   ├── 📁 lawcase-quartz/        # 定时任务
│   └── 📁 lawcase-system/        # 系统管理
├── 📁 admin-vue/                 # 管理员前端 (Vue.js)
├── 📁 user-admin-vue/           # 用户管理前端 (Vue.js)
├── 📁 user-front-vue/           # 用户前端 (Nuxt.js)
├── 📁 app/                      # 移动端应用 (uni-app)
├── 📁 sql/                      # 数据库脚本
├── 📁 doc/                      # 项目文档
├── 📄 start.sh                  # Linux启动脚本
├── 📄 start.bat                 # Windows启动脚本
└── 📄 快速启动指南.md            # 启动说明文档
```

## 主要目录结构及功能

### 🔧 **后端核心模块**

#### 1. **jeeplus/** - 主应用后端
- **jeeplus-web/** - 启动模块
  - 包含主启动类 `JeeplusWebApplication`
  - 端口：8081，上下文路径：/law
  - 主要运行入口

- **jeeplus-platform/** - 平台核心模块
  - **jeeplus-core/** - 核心功能模块（基础框架、工具类、通用组件）
  - **jeeplus-admin/** - 管理功能模块（系统管理、权限控制）

- **jeeplus-plugins/** - 插件模块
  - **jeeplus-monitor/** - 系统监控插件
  - **jeeplus-tools/** - 工具插件
  - **jeeplus-quartz/** - 定时任务插件
  - **jeeplus-datasource/** - 数据源插件
  - **jeeplus-oa/** - 办公自动化插件
  - **jeeplus-ureport/** - 报表插件
  - **jeeplus-wps/** - WPS文档处理插件

- **jeeplus-module/** - 业务模块
  - **jeeplus-case/** - 案件管理核心模块
  - **jeeplus-api/** - API接口模块（移动端接口）

#### 2. **jeeplus-ord/** - 订单管理模块（基于若依框架）
- **lawcase-admin/** - 订单管理后台
- **lawcase-common/** - 通用组件
- **lawcase-framework/** - 框架核心
- **lawcase-generator/** - 代码生成器
- **lawcase-mall/** - 商城订单模块
- **lawcase-quartz/** - 定时任务
- **lawcase-system/** - 系统管理

### 🎨 **前端模块**

#### 1. **admin-vue/** - 管理员后台前端
- 技术栈：Vue.js + Element UI
- 功能：系统管理、案件管理、用户管理等
- 端口：80
- 描述：律管云管理系统

#### 2. **user-admin-vue/** - 用户管理前端
- 技术栈：Vue.js + Element UI
- 功能：用户端管理界面
- 端口：8080

#### 3. **user-front-vue/** - 用户前端
- 技术栈：Nuxt.js + Element UI
- 功能：用户门户网站
- 端口：3000
- 描述：律管云用户前端

#### 4. **app/** - 移动端应用
- 技术栈：uni-app
- 功能：移动端案件管理应用
- 支持多平台（iOS、Android、H5、小程序）

### 📁 **其他重要目录**

#### 1. **sql/** - 数据库脚本
- 包含数据库初始化脚本
- 历史版本脚本

#### 2. **doc/** - 项目文档
- 包含系统相关文档和说明

## 🔥 **后端主要业务模块详解**

### 1. **案件管理模块** (jeeplus-case)
- **核心功能**：
  - 案件信息管理（新增、编辑、查询、删除）
  - 案件程序管理
  - 案件当事人管理
  - 案件承办人员管理
  - 案件文件管理
  - 案件关联关系管理
  - 案件审核流程
  - 案件结案归档

- **主要类**：
  - `CaseController` - 案件控制器
  - `CaseService` - 案件业务逻辑
  - `CaseMapper` - 数据访问层
  - `CaseVO` - 案件视图对象

### 2. **API模块** (jeeplus-api)
- **功能**：为移动端提供RESTful API接口
- **主要接口**：
  - 案件管理API
  - 用户认证API
  - 文件上传API

### 3. **订单管理模块** (jeeplus-ord)
- **功能**：
  - 订单创建和管理
  - 支付处理
  - 订单状态跟踪
  - 用户管理

- **主要类**：
  - `PayOrdController` - 订单控制器
  - `IPayOrdService` - 订单服务接口

### 4. **系统管理模块**
- **功能**：
  - 用户管理
  - 角色权限管理
  - 菜单管理
  - 字典管理
  - 系统配置

### 5. **插件模块**
- **监控插件** (jeeplus-monitor)：系统性能监控、服务器状态监控
- **定时任务插件** (jeeplus-quartz)：任务调度管理、定时任务配置
- **报表插件** (jeeplus-ureport)：数据报表生成、图表展示
- **办公自动化插件** (jeeplus-oa)：OA功能支持、流程管理
- **工具插件** (jeeplus-tools)：系统工具集合、代码生成
- **数据源插件** (jeeplus-datasource)：多数据源管理
- **WPS插件** (jeeplus-wps)：WPS文档在线编辑

## 🗂 **详细模块功能说明**

### **主应用模块 (jeeplus)**

#### jeeplus-web (启动模块)
- **功能**：项目启动入口，包含Spring Boot主类
- **配置文件**：application.yml、application-development.yml
- **端口**：8081
- **上下文路径**：/law

#### jeeplus-platform (平台核心)
- **jeeplus-core**：
  - 基础框架组件
  - 通用工具类
  - 数据访问层基类
  - 缓存管理
  - 异常处理
- **jeeplus-admin**：
  - 系统管理功能
  - 用户权限管理
  - 菜单管理
  - 角色管理

#### jeeplus-module (业务模块)
- **jeeplus-case**：
  - 案件CRUD操作
  - 案件流程管理
  - 案件文档管理
  - 案件统计分析
- **jeeplus-api**：
  - 移动端API接口
  - 用户认证接口
  - 文件上传接口

### **订单管理模块 (jeeplus-ord)**

#### 基于若依(RuoYi)框架构建
- **lawcase-admin**：订单管理后台界面
- **lawcase-mall**：电商订单核心业务
- **lawcase-system**：系统基础功能
- **lawcase-framework**：框架核心组件
- **lawcase-common**：通用工具和组件
- **lawcase-generator**：代码自动生成工具
- **lawcase-quartz**：定时任务调度

### **前端应用模块**

#### admin-vue (管理员后台)
- **技术栈**：Vue 2.x + Element UI + Vue Router + Vuex
- **主要功能**：
  - 案件管理界面
  - 用户权限管理
  - 系统配置管理
  - 数据统计报表
- **构建工具**：Vue CLI
- **开发端口**：80

#### user-admin-vue (用户管理前端)
- **技术栈**：Vue 2.x + Element UI
- **主要功能**：
  - 用户信息管理
  - 案件查看
  - 个人中心
- **开发端口**：8080

#### user-front-vue (用户门户)
- **技术栈**：Nuxt.js + Element UI + Axios
- **主要功能**：
  - 用户门户网站
  - 案件查询
  - 在线服务
- **特点**：支持SSR服务端渲染
- **开发端口**：3000

#### app (移动端应用)
- **技术栈**：uni-app + uView UI
- **支持平台**：
  - iOS App
  - Android App
  - H5网页
  - 微信小程序
  - 支付宝小程序
- **主要功能**：
  - 移动端案件管理
  - 在线咨询
  - 文件上传

## 🛠 **技术架构**

### 后端技术栈
- **框架**：Spring Boot 2.3.5
- **持久层**：MyBatis
- **安全框架**：Shiro
- **定时任务**：Quartz
- **数据库**：MySQL/PostgreSQL/Oracle/SQL Server
- **缓存**：Redis/EhCache

### 前端技术栈
- **管理端**：Vue.js + Element UI
- **用户端**：Nuxt.js + Element UI
- **移动端**：uni-app + uView UI

### 部署架构
- **开发环境**：Maven + Node.js
- **生产环境**：支持Docker容器化部署
- **数据库**：支持多种数据库
- **文件存储**：本地存储/阿里云OSS/MinIO

## 🚀 **项目特点**

1. **模块化设计**：采用Maven多模块架构，便于维护和扩展
2. **前后端分离**：支持多端访问，提供统一的API接口
3. **插件化架构**：核心功能通过插件方式扩展
4. **多数据库支持**：支持主流数据库
5. **移动端支持**：uni-app实现多平台移动应用
6. **权限管理**：基于Shiro的细粒度权限控制
7. **工作流支持**：案件审核流程管理

## 📋 **访问地址**

- **后端服务**: http://localhost:8081/law/
- **API文档**: http://localhost:8081/law/doc.html
- **管理员前端**: http://localhost:80
- **用户管理前端**: http://localhost:8080
- **用户前端**: http://localhost:3000

## 🗄 **数据库设计**

### 主要数据表
- **案件相关表**：
  - `law_case` - 案件基本信息
  - `law_case_concern_person` - 案件当事人
  - `law_case_undertake_person` - 案件承办人员
  - `law_case_file` - 案件文件
  - `law_case_program` - 案件程序
  - `law_case_cause` - 案由信息

- **系统管理表**：
  - `sys_user` - 用户信息
  - `sys_role` - 角色信息
  - `sys_menu` - 菜单权限
  - `sys_dict` - 数据字典

- **订单相关表**：
  - `pay_ord` - 订单信息
  - `pay_user` - 支付用户

### 数据库配置
- **默认数据库**：law_case_center
- **字符集**：utf8mb4
- **排序规则**：utf8mb4_unicode_ci
- **支持数据库**：MySQL 5.7+、PostgreSQL、Oracle、SQL Server

## ⚙️ **配置文件说明**

### 后端配置
- **主配置**：`application.yml`
- **开发环境**：`application-development.yml`
- **生产环境**：`application-production.yml`

### 关键配置项
```yaml
server:
  port: 8081
  servlet:
    context-path: /law

spring:
  datasource:
    url: *******************************************
    username: root
    password: 123456w
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 前端配置
- **Vue项目**：`vue.config.js`、`package.json`
- **Nuxt项目**：`nuxt.config.js`
- **uni-app**：`manifest.json`、`pages.json`

## 🚀 **启动方式**

### 后端启动
```bash
# 方式1：Maven命令
cd jeeplus
mvn spring-boot:run -pl jeeplus-web

# 方式2：Maven Wrapper
cd jeeplus/jeeplus-web
./mvnw spring-boot:run

# 方式3：jar包运行
mvn clean package -DskipTests
java -jar jeeplus-web/target/law.jar
```

### 前端启动
```bash
# 管理员前端
cd admin-vue
npm install
npm run dev

# 用户管理前端
cd user-admin-vue
yarn install
yarn serve

# 用户前端
cd user-front-vue
npm install
npm run dev
```

## 📝 **总结**

这个项目是一个功能完整的法律案件管理系统，涵盖了案件全生命周期管理、用户管理、订单管理等核心业务功能，适用于律师事务所、法律服务机构等场景。

### 系统优势
1. **架构清晰**：模块化设计，职责分明
2. **技术先进**：采用主流技术栈，易于维护
3. **功能完整**：覆盖案件管理全流程
4. **多端支持**：Web端、移动端全覆盖
5. **扩展性强**：插件化架构，便于功能扩展
6. **部署灵活**：支持传统部署和容器化部署

### 适用场景
- 律师事务所案件管理
- 法律服务机构业务管理
- 企业法务部门案件跟踪
- 法律咨询平台业务支撑
